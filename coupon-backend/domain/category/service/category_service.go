package service

import (
	"context"
	"coupon-backend/domain/category/entity"
	"coupon-backend/domain/category/repository"
	"coupon-backend/infra/cache"
	"coupon-backend/infra/ecode"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type CategoryService interface {
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error)
	GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*entity.Category, *ecode.Error)
	GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error)
	GetCategoryListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Category, *ecode.Error)
	CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	DeleteCategory(ctx *gin.Context, id uint64) *ecode.Error
	GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error)
	BatchCreateCategories(ctx *gin.Context, categories []*entity.Category) *ecode.Error
	BatchUpdateCategories(ctx *gin.Context, categories []*entity.Category) *ecode.Error

	// Sitemap优化方法
	GetCategoriesForSitemap(ctx *gin.Context, limit, offset int) ([]*entity.Category, *ecode.Error)
	GetCategoriesCountForSitemap(ctx *gin.Context) (int64, *ecode.Error)
}

type CategoryServiceImpl struct {
	repo repository.CategoryRepository
	// 缓存服务
	cacheService cache.CacheService
	// 本地缓存
	localCache     map[string]interface{}
	localCacheTTL  map[string]time.Time
	localCacheLock sync.RWMutex
	// 清理控制
	cleanupDone chan bool
}

func NewCategoryService(repo repository.CategoryRepository, cacheService cache.CacheService) CategoryService {
	service := &CategoryServiceImpl{
		repo:           repo,
		cacheService:   cacheService,
		localCache:     make(map[string]interface{}),
		localCacheTTL:  make(map[string]time.Time),
		localCacheLock: sync.RWMutex{},
		cleanupDone:    make(chan bool),
	}

	// 启动缓存清理协程，防止内存泄漏
	go service.startCacheCleanup()

	return service
}

// GetCategoryDetailById 根据ID获取分类详情
func (s *CategoryServiceImpl) GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error) {
	return s.repo.GetCategoryDetailById(ctx, id)
}

// GetCategoryDetailBySlug 根据Slug获取分类详情
func (s *CategoryServiceImpl) GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*entity.Category, *ecode.Error) {
	return s.repo.GetCategoryDetailBySlug(ctx, slug)
}

// GetCategoryListByCondition 根据条件获取分类列表
func (s *CategoryServiceImpl) GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error) {
	return s.repo.GetCategoryListByCondition(ctx, condition)
}

// GetCategoryListByIDs 根据ID列表获取分类列表 - 带缓存优化
func (s *CategoryServiceImpl) GetCategoryListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Category, *ecode.Error) {
	if len(ids) == 0 {
		return []*entity.Category{}, nil
	}

	// 去重
	uniqueIDs := s.removeDuplicateUint64(ids)
	result := make([]*entity.Category, 0, len(uniqueIDs))
	var missingIDs []uint64

	// 先从本地缓存获取
	s.localCacheLock.RLock()
	for _, id := range uniqueIDs {
		cacheKey := s.getCategoryCacheKey(id)
		if cachedData, exists := s.localCache[cacheKey]; exists {
			if ttl, ttlExists := s.localCacheTTL[cacheKey]; ttlExists && time.Now().Before(ttl) {
				if category, ok := cachedData.(*entity.Category); ok {
					result = append(result, category)
					continue
				}
			}
		}
		missingIDs = append(missingIDs, id)
	}
	s.localCacheLock.RUnlock()

	if len(missingIDs) == 0 {
		return result, nil
	}

	// 从Redis缓存获取
	if s.cacheService != nil {
		var stillMissingIDs []uint64
		for _, id := range missingIDs {
			cacheKey := s.getCategoryCacheKey(id)
			var category entity.Category
			if err := s.cacheService.Get(context.Background(), cacheKey, &category); err == nil {
				result = append(result, &category)
				// 更新本地缓存
				s.updateLocalCache(cacheKey, &category)
			} else {
				stillMissingIDs = append(stillMissingIDs, id)
			}
		}
		missingIDs = stillMissingIDs
	}

	// 从数据库获取剩余的
	if len(missingIDs) > 0 {
		categories, err := s.repo.GetCategoryListByIDs(ctx, missingIDs)
		if err != nil {
			return nil, err
		}

		// 批量写入缓存
		for _, category := range categories {
			result = append(result, category)
			cacheKey := s.getCategoryCacheKey(uint64(category.ID))

			// 更新本地缓存
			s.updateLocalCache(cacheKey, category)

			// 异步写入Redis缓存
			if s.cacheService != nil {
				go func(key string, data *entity.Category) {
					if err := s.cacheService.Set(context.Background(), key, data, 10*time.Minute); err != nil {
						log.Printf("Failed to cache category %d: %v", data.ID, err)
					}
				}(cacheKey, category)
			}
		}
	}

	return result, nil
}

// CreateCategory 创建分类
func (s *CategoryServiceImpl) CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	return s.repo.CreateCategory(ctx, category)
}

// UpdateCategory 更新分类
func (s *CategoryServiceImpl) UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	return s.repo.UpdateCategory(ctx, category)
}

// DeleteCategory 删除分类
func (s *CategoryServiceImpl) DeleteCategory(ctx *gin.Context, id uint64) *ecode.Error {
	return s.repo.DeleteCategory(ctx, id)
}

// GetCategoryCount 获取分类总数
func (s *CategoryServiceImpl) GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error) {
	return s.repo.GetCategoryCount(ctx)
}

// BatchCreateCategories 批量创建分类
func (s *CategoryServiceImpl) BatchCreateCategories(ctx *gin.Context, categories []*entity.Category) *ecode.Error {
	return s.repo.BatchCreateCategories(ctx, categories)
}

// BatchUpdateCategories 批量更新分类
func (s *CategoryServiceImpl) BatchUpdateCategories(ctx *gin.Context, categories []*entity.Category) *ecode.Error {
	return s.repo.BatchUpdateCategories(ctx, categories)
}

// startCacheCleanup 启动缓存清理协程，防止内存泄漏
func (s *CategoryServiceImpl) startCacheCleanup() {
	ticker := time.NewTicker(2 * time.Minute) // 每2分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.cleanExpiredCache()
		case <-s.cleanupDone:
			log.Println("CategoryService cache cleanup stopped")
			return
		}
	}
}

// cleanExpiredCache 清理过期缓存
func (s *CategoryServiceImpl) cleanExpiredCache() {
	s.localCacheLock.Lock()
	defer s.localCacheLock.Unlock()

	now := time.Now()
	expiredKeys := make([]string, 0)

	// 找出过期的键
	for key, ttl := range s.localCacheTTL {
		if now.After(ttl) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	// 删除过期数据
	for _, key := range expiredKeys {
		delete(s.localCache, key)
		delete(s.localCacheTTL, key)
	}

	// 记录清理情况
	if len(expiredKeys) > 0 {
		log.Printf("Cleaned %d expired cache entries from CategoryService", len(expiredKeys))
	}
}

// StopCacheCleanup 停止缓存清理（用于优雅关闭）
func (s *CategoryServiceImpl) StopCacheCleanup() {
	select {
	case s.cleanupDone <- true:
	default:
		// 通道已关闭或已发送信号
	}
}

// removeDuplicateUint64 去重uint64切片
func (s *CategoryServiceImpl) removeDuplicateUint64(slice []uint64) []uint64 {
	keys := make(map[uint64]bool)
	result := make([]uint64, 0, len(slice))
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	return result
}

// getCategoryCacheKey 生成分类缓存键
func (s *CategoryServiceImpl) getCategoryCacheKey(id uint64) string {
	return fmt.Sprintf("category:detail:%d", id)
}

// updateLocalCache 更新本地缓存
func (s *CategoryServiceImpl) updateLocalCache(key string, data interface{}) {
	s.localCacheLock.Lock()
	defer s.localCacheLock.Unlock()
	s.localCache[key] = data
	s.localCacheTTL[key] = time.Now().Add(5 * time.Minute) // 本地缓存5分钟
}

// GetCategoriesForSitemap 专门为sitemap优化的分类查询 - 直接调用repository层的优化方法
func (s *CategoryServiceImpl) GetCategoriesForSitemap(ctx *gin.Context, limit, offset int) ([]*entity.Category, *ecode.Error) {
	return s.repo.GetCategoriesForSitemap(ctx, limit, offset)
}

// GetCategoriesCountForSitemap 获取sitemap可用的分类总数 - 直接调用repository层的优化方法
func (s *CategoryServiceImpl) GetCategoriesCountForSitemap(ctx *gin.Context) (int64, *ecode.Error) {
	return s.repo.GetCategoriesCountForSitemap(ctx)
}
