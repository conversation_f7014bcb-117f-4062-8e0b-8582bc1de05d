package service

import (
	"context"
	"coupon-backend/domain/brand/entity"
	"coupon-backend/domain/brand/repository"
	"coupon-backend/infra/cache"
	"coupon-backend/infra/ecode"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type BrandService interface {
	GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error)
	GetBrandDetailByUniqueName(ctx *gin.Context, uniqueName string) (*entity.Brand, *ecode.Error)

	GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error)
	// GetBrandListByIDs 批量获取商家信息
	GetBrandListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Brand, *ecode.Error)

	CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error
	UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error
	DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error
	GetBrandCount(ctx *gin.Context) (int64, *ecode.Error)

	// BatchCreateBrands 批量创建商家
	BatchCreateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error
	// BatchUpdateBrands 批量更新商家
	BatchUpdateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error

	// Sitemap优化方法
	GetBrandsForSitemap(ctx *gin.Context, limit, offset int) ([]*entity.Brand, *ecode.Error)
	GetBrandsCountForSitemap(ctx *gin.Context) (int64, *ecode.Error)
}

type BrandServiceImpl struct {
	repo repository.BrandRepository
	// 缓存服务
	cacheService cache.CacheService
	// 本地缓存
	localCache     map[string]interface{}
	localCacheTTL  map[string]time.Time
	localCacheLock sync.RWMutex
	// 清理控制
	cleanupDone chan bool
	// 异步写入控制
	asyncWriteChan chan asyncWriteTask
	asyncWriteDone chan bool
}

// asyncWriteTask 异步写入任务
type asyncWriteTask struct {
	key  string
	data interface{}
	ttl  time.Duration
}

func NewBrandService(repo repository.BrandRepository, cacheService cache.CacheService) BrandService {
	service := &BrandServiceImpl{
		repo:           repo,
		cacheService:   cacheService,
		localCache:     make(map[string]interface{}),
		localCacheTTL:  make(map[string]time.Time),
		localCacheLock: sync.RWMutex{},
		cleanupDone:    make(chan bool),
		asyncWriteChan: make(chan asyncWriteTask, 100), // 缓冲100个任务
		asyncWriteDone: make(chan bool),
	}

	// 启动缓存清理协程，防止内存泄漏
	go service.startCacheCleanup()

	// 启动异步写入协程，防止goroutine累积
	go service.startAsyncWriter()

	return service
}

// GetBrandDetailById 根据ID获取商家详情
func (s *BrandServiceImpl) GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error) {
	return s.repo.GetBrandDetailById(ctx, id)
}

// GetBrandDetailByUniqueName 根据UniqueName获取商家详情
func (s *BrandServiceImpl) GetBrandDetailByUniqueName(ctx *gin.Context, uniqueName string) (*entity.Brand, *ecode.Error) {
	return s.repo.GetBrandDetailByUniqueName(ctx, uniqueName)
}

// GetBrandListByCondition 根据条件获取商家列表
func (s *BrandServiceImpl) GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error) {
	return s.repo.GetBrandListByCondition(ctx, condition)
}

// GetBrandListByIDs 根据ID列表获取商家列表 - 带缓存优化
func (s *BrandServiceImpl) GetBrandListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Brand, *ecode.Error) {
	if len(ids) == 0 {
		return []*entity.Brand{}, nil
	}

	// 去重
	uniqueIDs := s.removeDuplicateUint64(ids)
	result := make([]*entity.Brand, 0, len(uniqueIDs))
	var missingIDs []uint64

	// 先从本地缓存获取
	s.localCacheLock.RLock()
	for _, id := range uniqueIDs {
		cacheKey := s.getBrandCacheKey(id)
		if cachedData, exists := s.localCache[cacheKey]; exists {
			if ttl, ttlExists := s.localCacheTTL[cacheKey]; ttlExists && time.Now().Before(ttl) {
				if brand, ok := cachedData.(*entity.Brand); ok {
					result = append(result, brand)
					continue
				}
			}
		}
		missingIDs = append(missingIDs, id)
	}
	s.localCacheLock.RUnlock()

	if len(missingIDs) == 0 {
		return result, nil
	}

	// 从Redis缓存获取
	if s.cacheService != nil {
		var stillMissingIDs []uint64
		for _, id := range missingIDs {
			cacheKey := s.getBrandCacheKey(id)
			var brand entity.Brand
			if err := s.cacheService.Get(context.Background(), cacheKey, &brand); err == nil {
				result = append(result, &brand)
				// 更新本地缓存
				s.updateLocalCache(cacheKey, &brand)
			} else {
				stillMissingIDs = append(stillMissingIDs, id)
			}
		}
		missingIDs = stillMissingIDs
	}

	// 从数据库获取剩余的
	if len(missingIDs) > 0 {
		brands, err := s.repo.GetBrandListByIDs(ctx, missingIDs)
		if err != nil {
			return nil, err
		}

		// 批量写入缓存
		for _, brand := range brands {
			result = append(result, brand)
			cacheKey := s.getBrandCacheKey(uint64(brand.ID))

			// 更新本地缓存
			s.updateLocalCache(cacheKey, brand)

			// 异步写入Redis缓存（使用任务队列，避免goroutine累积）
			if s.cacheService != nil {
				task := asyncWriteTask{
					key:  cacheKey,
					data: brand,
					ttl:  10 * time.Minute,
				}
				select {
				case s.asyncWriteChan <- task:
					// 成功加入队列
				default:
					// 队列满时丢弃（避免阻塞）
					log.Printf("Async write queue full, dropping cache write for key: %s", cacheKey)
				}
			}
		}
	}

	return result, nil
}

// CreateBrand 创建商家
func (s *BrandServiceImpl) CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	return s.repo.CreateBrand(ctx, brand)
}

// UpdateBrand 更新商家
func (s *BrandServiceImpl) UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	return s.repo.UpdateBrand(ctx, brand)
}

// DeleteBrand 删除商家
func (s *BrandServiceImpl) DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error {
	return s.repo.DeleteBrand(ctx, id)
}

// GetBrandCount 获取商家总数
func (s *BrandServiceImpl) GetBrandCount(ctx *gin.Context) (int64, *ecode.Error) {
	return s.repo.GetBrandCount(ctx)
}

// BatchCreateBrands 批量创建商家
func (s *BrandServiceImpl) BatchCreateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error {
	return s.repo.BatchCreateBrands(ctx, brands)
}

// BatchUpdateBrands 批量更新商家
func (s *BrandServiceImpl) BatchUpdateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error {
	return s.repo.BatchUpdateBrands(ctx, brands)
}

// 辅助方法

// removeDuplicateUint64 去重uint64切片
func (s *BrandServiceImpl) removeDuplicateUint64(slice []uint64) []uint64 {
	keys := make(map[uint64]bool)
	var result []uint64
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	return result
}

// getBrandCacheKey 生成品牌缓存键
func (s *BrandServiceImpl) getBrandCacheKey(id uint64) string {
	return fmt.Sprintf("brand:detail:%d", id)
}

// updateLocalCache 更新本地缓存
func (s *BrandServiceImpl) updateLocalCache(key string, data interface{}) {
	s.localCacheLock.Lock()
	defer s.localCacheLock.Unlock()
	s.localCache[key] = data
	s.localCacheTTL[key] = time.Now().Add(5 * time.Minute) // 本地缓存5分钟
}

// startCacheCleanup 启动缓存清理协程，防止内存泄漏
func (s *BrandServiceImpl) startCacheCleanup() {
	ticker := time.NewTicker(2 * time.Minute) // 每2分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.cleanExpiredCache()
		case <-s.cleanupDone:
			log.Println("BrandService cache cleanup stopped")
			return
		}
	}
}

// cleanExpiredCache 清理过期缓存
func (s *BrandServiceImpl) cleanExpiredCache() {
	s.localCacheLock.Lock()
	defer s.localCacheLock.Unlock()

	now := time.Now()
	expiredKeys := make([]string, 0)

	// 找出过期的键
	for key, ttl := range s.localCacheTTL {
		if now.After(ttl) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	// 删除过期数据
	for _, key := range expiredKeys {
		delete(s.localCache, key)
		delete(s.localCacheTTL, key)
	}

	// 记录清理情况
	if len(expiredKeys) > 0 {
		log.Printf("Cleaned %d expired cache entries from BrandService", len(expiredKeys))
	}
}

// startAsyncWriter 启动异步写入协程，防止goroutine累积
func (s *BrandServiceImpl) startAsyncWriter() {
	for {
		select {
		case task := <-s.asyncWriteChan:
			// 执行异步写入
			if err := s.cacheService.Set(context.Background(), task.key, task.data, task.ttl); err != nil {
				log.Printf("Failed to write cache for key %s: %v", task.key, err)
			}
		case <-s.asyncWriteDone:
			log.Println("BrandService async writer stopped")
			return
		}
	}
}

// StopCacheCleanup 停止缓存清理（用于优雅关闭）
func (s *BrandServiceImpl) StopCacheCleanup() {
	select {
	case s.cleanupDone <- true:
	default:
		// 通道已关闭或已发送信号
	}
}

// StopAsyncWriter 停止异步写入（用于优雅关闭）
func (s *BrandServiceImpl) StopAsyncWriter() {
	select {
	case s.asyncWriteDone <- true:
	default:
		// 通道已关闭或已发送信号
	}
}

// GetBrandsForSitemap 专门为sitemap优化的品牌查询 - 直接调用repository层的优化方法
func (s *BrandServiceImpl) GetBrandsForSitemap(ctx *gin.Context, limit, offset int) ([]*entity.Brand, *ecode.Error) {
	return s.repo.GetBrandsForSitemap(ctx, limit, offset)
}

// GetBrandsCountForSitemap 获取sitemap可用的品牌总数 - 直接调用repository层的优化方法
func (s *BrandServiceImpl) GetBrandsCountForSitemap(ctx *gin.Context) (int64, *ecode.Error) {
	return s.repo.GetBrandsCountForSitemap(ctx)
}
