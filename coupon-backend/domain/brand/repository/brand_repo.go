package repository

import (
	"coupon-backend/domain/brand/entity"
	"coupon-backend/infra/ecode"

	"github.com/gin-gonic/gin"
)

// BrandRepository 商家仓储接口
type BrandRepository interface {
	GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error)
	GetBrandDetailByUniqueName(ctx *gin.Context, uniqueName string) (*entity.Brand, *ecode.Error)
	GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error)
	GetBrandCount(ctx *gin.Context) (int64, *ecode.Error)
	GetBrandListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Brand, *ecode.Error)
	CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error
	UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error
	DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error
	UpdateBrandStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error
	BatchCreateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error
	BatchUpdateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error

	// 同步相关方法
	GetBrandsByPlatform(ctx *gin.Context, platformType string) ([]*entity.Brand, *ecode.Error)
	FindBrandByPlatformAndMerchantID(ctx *gin.Context, platformType, merchantID string) (*entity.Brand, *ecode.Error)
	GetAllBrands(ctx *gin.Context) ([]*entity.Brand, *ecode.Error)
	GetAllBrandsIncludingInactive(ctx *gin.Context) ([]*entity.Brand, *ecode.Error)
	FindBrandsBySiteURL(ctx *gin.Context, siteURL string) ([]*entity.Brand, *ecode.Error)
	BatchUpdateBrandStatus(ctx *gin.Context, brandIDs []uint, status int) *ecode.Error

	// 更新计数字段
	UpdateBrandCouponCount(ctx *gin.Context, brandID uint, count int) *ecode.Error
	UpdateBrandDealCount(ctx *gin.Context, brandID uint, count int) *ecode.Error

	// Sitemap优化方法
	GetBrandsForSitemap(ctx *gin.Context, limit, offset int) ([]*entity.Brand, *ecode.Error)
	GetBrandsCountForSitemap(ctx *gin.Context) (int64, *ecode.Error)
}
