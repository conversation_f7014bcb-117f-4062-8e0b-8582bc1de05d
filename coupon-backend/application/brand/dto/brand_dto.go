package dto

import (
	"coupon-backend/application/category/dto"
	"coupon-backend/domain/brand/entity"
	categoryEntity "coupon-backend/domain/category/entity"
	"time"
)

type GetBrandListReq struct {
	Page       int    `json:"page" form:"page"`
	PageSize   int    `json:"page_size" form:"page_size"`
	Search     string `json:"search" form:"search"`
	Offset     int    `json:"offset"`
	Sort       string `json:"sort" form:"sort"`
	Featured   bool   `json:"featured" form:"featured"`
	CategoryID uint64 `json:"category_id" form:"category_id"`
	StartsWith string `json:"starts_with" form:"starts_with"`
}

// 响应对象

// BrandCouponResp 品牌接口中的优惠券响应（避免循环导入，与CouponDetailResp相同但不包含Brand字段）
type BrandCouponResp struct {
	ID          uint       `json:"id"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	BrandID     uint       `json:"brand_id"`
	CategoryID  uint       `json:"category_id"`
	Code        string     `json:"code"`
	IsFeatured  bool       `json:"is_featured"`
	IsExclusive bool       `json:"is_exclusive"`
	Discount    string     `json:"discount"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	StartDate   *time.Time `json:"start_date"`
	EndDate     *time.Time `json:"end_date"`
	Status      int        `json:"status"`
}

// BrandDealResp 品牌接口中的优惠活动响应（避免循环导入，与DealDetailResp相同但不包含Brand字段）
type BrandDealResp struct {
	ID          uint       `json:"id"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	BrandID     uint       `json:"brand_id"`
	CategoryID  uint       `json:"category_id"`
	Code        string     `json:"code"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Img         string     `json:"img"`
	IsHotDeal   bool       `json:"is_hot_deal"`
	IsFeatured  bool       `json:"is_featured"`
	Discount    string     `json:"discount"`
	OriginURL   string     `json:"origin_url"`
	TrackingUrl string     `json:"tracking_url"`
	StartDate   *time.Time `json:"start_date"`
	EndDate     *time.Time `json:"end_date"`
	Status      int        `json:"status"`
}

type BrandDetailResp struct {
	ID                 uint                    `json:"id"`
	CreatedAt          time.Time               `json:"created_at"`
	UpdatedAt          time.Time               `json:"updated_at"`
	UniqueName         string                  `json:"unique_name"`
	Name               string                  `json:"name"`
	Description        string                  `json:"description"`
	Logo               string                  `json:"logo"`
	SiteURL            string                  `json:"site_url"`
	OriginURL          string                  `json:"origin_url"`
	CategoryID         uint                    `json:"category_id"`         // 分类ID
	Category           *dto.CategoryDetailResp `json:"category"`            // 分类信息
	CountryName        string                  `json:"country"`             // 国家名字
	Featured           bool                    `json:"featured"`            // 是否推荐商家
	SupportedCountries []string                `json:"supported_countries"` // 支持返利的国家（仅仅用于展示，实际没关系）
	Status             int                     `json:"status"`
	TrackingURL        string                  `json:"tracking_url"`
	TotalCoupons       int                     `json:"total_coupons"`
	TotalDeals         int                     `json:"total_deals"`
	Coupons            []*BrandCouponResp      `json:"coupons"` // 该品牌的优惠券列表
	Deals              []*BrandDealResp        `json:"deals"`   // 该品牌的优惠活动列表
}

type BrandListResp struct {
	Total     int64              `json:"total"`
	Page      int                `json:"page"`
	PageSize  int                `json:"page_size"`
	BrandList []*BrandDetailResp `json:"brand_list"`
}

// Dto2ConditionGetBrandList 获取商家列表请求转换为 condition
func (req *GetBrandListReq) Dto2ConditionGetBrandList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Sort != "" {
		condition["sort"] = req.Sort
	}
	if req.Featured {
		condition["featured"] = req.Featured
	}
	if req.CategoryID > 0 {
		condition["category_id"] = req.CategoryID
	}
	if req.StartsWith != "" {
		condition["starts_with"] = req.StartsWith
	}
	return condition
}

func Entity2DtoBrandDetailResp(brand *entity.Brand, category *categoryEntity.Category) (resp *BrandDetailResp) {
	return Entity2DtoBrandDetailRespWithCouponsDeals(brand, category, nil, nil)
}

func Entity2DtoBrandDetailRespWithCouponsDeals(brand *entity.Brand, category *categoryEntity.Category, coupons []*BrandCouponResp, deals []*BrandDealResp) (resp *BrandDetailResp) {
	resp = &BrandDetailResp{}
	resp.ID = brand.ID
	resp.Name = brand.Name
	resp.UniqueName = brand.UniqueName
	resp.Description = brand.Description
	resp.Logo = brand.Logo
	resp.SiteURL = brand.SiteURL
	resp.OriginURL = brand.OriginURL
	resp.CategoryID = brand.CategoryID
	resp.CountryName = brand.CountryName

	// 转换分类信息
	if category != nil {
		resp.Category = dto.Entity2DtoCategoryDetailResp(category)
	} else {
		resp.Category = &dto.CategoryDetailResp{}
	}
	resp.Featured = brand.Featured
	resp.SupportedCountries = brand.SupportedCountries
	resp.TotalCoupons = brand.TotalCoupons
	resp.TotalDeals = brand.TotalDeals
	resp.Status = brand.Status
	resp.TrackingURL = brand.TrackingURL
	resp.CreatedAt = brand.CreatedAt
	resp.UpdatedAt = brand.UpdatedAt

	// 设置优惠券和优惠活动列表
	if coupons != nil {
		resp.Coupons = coupons
	} else {
		resp.Coupons = []*BrandCouponResp{}
	}

	if deals != nil {
		resp.Deals = deals
	} else {
		resp.Deals = []*BrandDealResp{}
	}

	return resp
}

func Entity2DtoBrandListResp(pageSize int, page int, total int64, brandList []*entity.Brand, categoryMap map[uint64]*categoryEntity.Category) (resp *BrandListResp) {
	resp = &BrandListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range brandList {
		if category, ok := categoryMap[uint64(brandList[i].CategoryID)]; ok {
			resp.BrandList = append(resp.BrandList, Entity2DtoBrandDetailResp(brandList[i], category))
		} else {
			category = &categoryEntity.Category{}
			resp.BrandList = append(resp.BrandList, Entity2DtoBrandDetailResp(brandList[i], category))
		}
	}
	return resp
}

// BrandSitemapResp sitemap专用的品牌响应 - 只包含sitemap需要的字段
type BrandSitemapResp struct {
	ID         uint      `json:"id"`
	UniqueName string    `json:"unique_name"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// Entity2DtoBrandSitemapResp 将品牌实体转换为sitemap响应
func Entity2DtoBrandSitemapResp(brand *entity.Brand) *BrandSitemapResp {
	if brand == nil {
		return nil
	}

	return &BrandSitemapResp{
		ID:         brand.ID,
		UniqueName: brand.UniqueName,
		UpdatedAt:  brand.UpdatedAt,
	}
}
