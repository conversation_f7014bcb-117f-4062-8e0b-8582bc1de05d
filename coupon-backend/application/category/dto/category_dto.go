package dto

import (
	"coupon-backend/domain/category/entity"
	"time"
)

// GetCategoryListReq 获取分类列表请求
type GetCategoryListReq struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	Search   string `json:"search" form:"search"`
	Sort     string `json:"sort" form:"sort"`
}

// CategoryDetailResp 分类详情响应
type CategoryDetailResp struct {
	ID        uint      `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Slug      string    `json:"slug"`
	Name      string    `json:"name"`
	Icon      string    `json:"icon"`
}

// CategoryListResp 分类列表响应
type CategoryListResp struct {
	Total        int64                 `json:"total"`
	Page         int                   `json:"page"`
	PageSize     int                   `json:"page_size"`
	CategoryList []*CategoryDetailResp `json:"category_list"`
}

// Dto2ConditionGetCategoryList 获取分类列表请求转换为 condition
func (req *GetCategoryListReq) Dto2ConditionGetCategoryList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Sort != "" {
		condition["sort"] = req.Sort
	}
	return condition
}

// Entity2DtoCategoryDetailResp 实体转换为DTO
func Entity2DtoCategoryDetailResp(category *entity.Category) *CategoryDetailResp {
	if category == nil {
		return &CategoryDetailResp{}
	}
	return &CategoryDetailResp{
		ID:        category.ID,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
		Slug:      category.Slug,
		Name:      category.Name,
		Icon:      category.Icon,
	}
}

// Entity2DtoCategoryListResp 实体列表转换为DTO
func Entity2DtoCategoryListResp(pageSize int, page int, total int64, categoryList []*entity.Category) *CategoryListResp {
	resp := &CategoryListResp{
		Total:    total,
		PageSize: pageSize,
		Page:     page,
	}
	for _, category := range categoryList {
		resp.CategoryList = append(resp.CategoryList, Entity2DtoCategoryDetailResp(category))
	}
	return resp
}

// CategorySitemapResp sitemap专用的分类响应 - 只包含sitemap需要的字段
type CategorySitemapResp struct {
	ID        uint      `json:"id"`
	Slug      string    `json:"slug"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Entity2DtoCategorySitemapResp 将分类实体转换为sitemap响应
func Entity2DtoCategorySitemapResp(category *entity.Category) *CategorySitemapResp {
	if category == nil {
		return nil
	}

	return &CategorySitemapResp{
		ID:        category.ID,
		Slug:      category.Slug,
		UpdatedAt: category.UpdatedAt,
	}
}
