# 生产环境配置
server:
  port: 8080
  mode: release
  read_timeout: 60s
  write_timeout: 60s

database:
  host: 1Panel-postgresql-Qp5o
  port: 5432
  user: user_7JSH6c
  password: password_k4j5ks
  db_name: maxcoupon
  ssl_mode: disable
  max_idle_conns: 20
  max_open_conns: 200
  max_lifetime: 3600s

redis:
  host: 1Panel-redis-PNHZ
  port: 6379
  password: redis_STydXm
  db: 0
  pool_size: 20
  min_idle_conns: 10
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s

log:
  level: info
  filename: logs/app.log
  max_size: 100
  max_age: 30
  compress: true

website:
  domain: gocoupons.org
  url: https://gocoupons.org
