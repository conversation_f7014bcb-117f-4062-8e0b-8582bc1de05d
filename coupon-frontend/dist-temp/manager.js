import { defaultTheme } from './default';
/**
 * 主题管理器
 * 负责主题的加载、切换和CSS变量生成
 */
export class ThemeManager {
    constructor(theme = defaultTheme) {
        this.cssVariables = {};
        this.currentTheme = theme;
        this.generateCSSVariables();
    }
    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
    /**
     * 设置新主题
     */
    setTheme(theme) {
        this.currentTheme = theme;
        this.generateCSSVariables();
    }
    /**
     * 生成CSS变量
     */
    generateCSSVariables() {
        const theme = this.currentTheme;
        const variables = {};
        // 生成颜色变量
        Object.entries(theme.colors).forEach(([colorName, shades]) => {
            Object.entries(shades).forEach(([shade, value]) => {
                variables[`--color-${colorName}-${shade}`] = value;
            });
        });
        // 生成具体用途的颜色变量
        this.generateSpecificColorVariables(theme.components, variables);
        // 生成渐变变量
        Object.entries(theme.gradients).forEach(([name, value]) => {
            if (typeof value === 'string') {
                variables[`--gradient-${name}`] = value;
            }
            else {
                Object.entries(value).forEach(([subName, subValue]) => {
                    variables[`--gradient-${name}-${subName}`] = subValue;
                });
            }
        });
        // 生成其他变量
        Object.entries(theme.borderRadius).forEach(([name, value]) => {
            variables[`--border-radius-${name}`] = value;
        });
        Object.entries(theme.spacing).forEach(([name, value]) => {
            variables[`--spacing-${name}`] = value;
        });
        Object.entries(theme.fontSize).forEach(([name, value]) => {
            variables[`--font-size-${name}`] = value;
        });
        this.cssVariables = variables;
    }
    /**
     * 生成具体用途的颜色变量
     */
    generateSpecificColorVariables(components, variables) {
        const processObject = (obj, prefix) => {
            Object.entries(obj).forEach(([key, value]) => {
                if (typeof value === 'string') {
                    variables[`--${prefix}-${key}`] = value;
                }
                else if (typeof value === 'object' && value !== null) {
                    processObject(value, `${prefix}-${key}`);
                }
            });
        };
        processObject(components, 'component');
    }
    /**
     * 获取CSS变量
     */
    getCSSVariables() {
        return this.cssVariables;
    }
    /**
     * 生成CSS变量字符串
     */
    getCSSVariablesString() {
        const variables = this.getCSSVariables();
        return Object.entries(variables)
            .map(([key, value]) => `  ${key}: ${value};`)
            .join('\n');
    }
    /**
     * 应用主题到DOM
     */
    applyThemeToDOM() {
        const root = document.documentElement;
        Object.entries(this.cssVariables).forEach(([key, value]) => {
            root.style.setProperty(key, value);
        });
    }
    /**
     * 获取特定颜色值
     */
    getColor(path) {
        const keys = path.split('.');
        let current = this.currentTheme;
        for (const key of keys) {
            current = current?.[key];
        }
        return (typeof current === 'string' ? current : '');
    }
    /**
     * 获取Tailwind类名映射
     */
    getTailwindClassMap() {
        // 移除未使用的变量
        return {
            // 主要按钮
            'btn-primary': 'bg-primary-600 hover:bg-primary-700 text-white border-primary-600',
            'btn-secondary': 'bg-secondary-600 hover:bg-secondary-700 text-white border-secondary-600',
            'btn-success': 'bg-success-600 hover:bg-success-700 text-white border-success-600',
            'btn-danger': 'bg-error-600 hover:bg-error-700 text-white border-error-600',
            // 文本颜色
            'text-primary': 'text-neutral-900',
            'text-secondary': 'text-neutral-700',
            'text-muted': 'text-neutral-500',
            // 背景颜色
            'bg-card': 'bg-white',
            'bg-secondary': 'bg-neutral-50',
            // 边框颜色
            'border-default': 'border-neutral-200',
            'border-focus': 'border-primary-500',
            // 阴影
            'shadow-hover': 'shadow-xl shadow-primary-200/50',
        };
    }
}
// 创建默认主题管理器实例
export const themeManager = new ThemeManager(defaultTheme);
// 导出便捷函数
export const getCurrentTheme = () => themeManager.getCurrentTheme();
export const getColor = (path) => themeManager.getColor(path);
export const getCSSVariables = () => themeManager.getCSSVariables();
export const applyTheme = (theme) => {
    themeManager.setTheme(theme);
    themeManager.applyThemeToDOM();
};
