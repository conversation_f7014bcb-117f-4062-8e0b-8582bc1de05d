import { themeManager } from './manager';
/**
 * 主题工具函数
 * 提供便捷的主题相关功能
 */
/**
 * 获取主题颜色值
 */
export function useThemeColor(path) {
    return themeManager.getColor(path);
}
/**
 * 获取主题相关的类名
 */
export function useThemeClasses() {
    const theme = themeManager.getCurrentTheme();
    return {
        // Brand卡片类名
        brandCard: {
            brandName: {
                text: `text-[${theme.components.brandCard.brandName.text}]`,
                border: `border-[${theme.components.brandCard.brandName.border}]`
            },
            discountBadge: {
                background: theme.components.brandCard.discountBadge.background,
                text: theme.components.brandCard.discountBadge.text
            },
            couponCount: {
                text: `text-[${theme.components.brandCard.couponCount.text}]`
            }
        },
        // Coupon卡片类名
        couponCard: {
            discountBadge: {
                style: {
                    background: theme.components.couponCard.discountBadge.background,
                    color: theme.components.couponCard.discountBadge.text
                }
            },
            button: {
                style: {
                    backgroundColor: theme.components.couponCard.button.background,
                    color: theme.components.couponCard.button.text,
                    borderColor: theme.components.couponCard.button.border
                },
                hoverStyle: {
                    backgroundColor: theme.components.couponCard.button.hover
                }
            },
            code: {
                text: `text-[${theme.components.couponCard.code.text}]`
            },
            brandName: {
                text: `text-[${theme.components.couponCard.brandName.text}]`
            },
            couponName: {
                text: `text-[${theme.components.couponCard.couponName.text}]`
            }
        },
        // 分类卡片类名
        categoryCard: {
            icon: {
                color: `text-[${theme.components.categoryCard.icon.color}]`
            },
            text: {
                color: `text-[${theme.components.categoryCard.text.color}]`
            }
        },
        // Deal卡片类名
        dealCard: {
            discountBadge: {
                style: {
                    background: theme.components.dealCard.discountBadge.background,
                    color: theme.components.dealCard.discountBadge.text
                }
            },
            button: {
                style: {
                    backgroundColor: theme.components.dealCard.button.background,
                    color: theme.components.dealCard.button.text,
                    borderColor: theme.components.dealCard.button.border
                },
                hoverStyle: {
                    backgroundColor: theme.components.dealCard.button.hover
                }
            }
        },
        // Coupon模态框类名
        couponModal: {
            card: {
                background: `bg-[${theme.components.couponModal.card.background}]`
            },
            code: {
                text: `text-[${theme.components.couponModal.code.text}]`
            },
            copyButton: {
                style: {
                    backgroundColor: theme.components.couponModal.copyButton.background,
                    color: theme.components.couponModal.copyButton.text
                },
                hoverStyle: {
                    backgroundColor: theme.components.couponModal.copyButton.hover
                }
            },
            actionButton: {
                style: {
                    backgroundColor: theme.components.couponModal.actionButton.background,
                    color: theme.components.couponModal.actionButton.text
                },
                hoverStyle: {
                    backgroundColor: theme.components.couponModal.actionButton.hover
                }
            },
            discountBadge: {
                style: {
                    background: theme.components.couponModal.discountBadge.background,
                    color: theme.components.couponModal.discountBadge.text
                }
            },
            brandName: {
                text: `text-[${theme.components.couponModal.brandName.text}]`
            },
            couponName: {
                text: `text-[${theme.components.couponModal.couponName.text}]`
            }
        },
        // Deal模态框类名
        dealModal: {
            card: {
                background: `bg-[${theme.components.dealModal.card.background}]`
            },
            actionButton: {
                style: {
                    backgroundColor: theme.components.dealModal.actionButton.background,
                    color: theme.components.dealModal.actionButton.text
                },
                hoverStyle: {
                    backgroundColor: theme.components.dealModal.actionButton.hover
                }
            },
            discountBadge: {
                style: {
                    background: theme.components.dealModal.discountBadge.background,
                    color: theme.components.dealModal.discountBadge.text
                }
            },
            brandName: {
                text: `text-[${theme.components.dealModal.brandName.text}]`
            },
            dealName: {
                text: `text-[${theme.components.dealModal.dealName.text}]`
            }
        },
        // 通用按钮类名
        commonButton: {
            primary: {
                background: `bg-[${theme.components.commonButton.primary.background}]`,
                text: `text-[${theme.components.commonButton.primary.text}]`,
                hover: `hover:bg-[${theme.components.commonButton.primary.hover}]`
            },
            secondary: {
                background: `bg-[${theme.components.commonButton.secondary.background}]`,
                text: `text-[${theme.components.commonButton.secondary.text}]`,
                hover: `hover:bg-[${theme.components.commonButton.secondary.hover}]`
            },
            tertiary: {
                background: `bg-transparent`,
                text: `text-[${theme.components.commonButton.tertiary.text}]`,
                border: `border-[${theme.components.commonButton.tertiary.border}]`,
                hover: `hover:bg-[${theme.components.commonButton.tertiary.hover}]`
            }
        },
        // 搜索框类名
        searchBox: {
            icon: {
                color: `text-[${theme.components.searchBox.icon.color}]`
            }
        },
        // Newsletter类名
        newsletter: {
            container: {
                style: {
                    background: theme.components.newsletter.container.background,
                    border: theme.components.newsletter.container.border
                }
            },
            title: {
                text: `text-[${theme.components.newsletter.title.text}]`
            },
            description: {
                text: `text-[${theme.components.newsletter.description.text}]`
            },
            button: {
                background: `bg-[${theme.components.newsletter.button.background}]`,
                text: `text-[${theme.components.newsletter.button.text}]`,
                hover: `hover:bg-[${theme.components.newsletter.button.hover}]`
            }
        },
        // 页脚类名
        footer: {
            icon: {
                color: `text-[${theme.components.footer.icon.color}]`
            },
            section: {
                title: `text-[${theme.components.footer.section.title}]`,
                text: `text-[${theme.components.footer.section.text}]`
            },
            link: {
                text: `text-[${theme.components.footer.link.text}]`,
                hover: `hover:text-[${theme.components.footer.link.hover}]`
            }
        },
        // Header类名
        header: {
            navLink: {
                text: `text-[${theme.components.header.navLink.text}]`,
                hover: `hover:text-[${theme.components.header.navLink.hover}]`
            },
            logo: {
                text: `text-[${theme.components.header.logo.text}]`
            }
        },
        // Homepage类名
        homepage: {
            hero: {
                style: {
                    background: theme.components.homepage.hero.background,
                    color: theme.components.homepage.hero.title
                }
            },
            section: {
                title: `text-[${theme.components.homepage.section.title}]`,
                subtitle: `text-[${theme.components.homepage.section.subtitle}]`
            }
        },
        // Pagination类名
        pagination: {
            button: {
                background: `bg-[${theme.components.pagination.button.background}]`,
                text: `text-[${theme.components.pagination.button.text}]`,
                hover: `hover:bg-[${theme.components.pagination.button.hover}]`,
                active: `bg-[${theme.components.pagination.button.active}]`
            }
        }
    };
}
/**
 * 生成CSS变量
 */
export function generateCSSVariables() {
    return `:root {\n${themeManager.getCSSVariablesString()}\n}`;
}
/**
 * 获取渐变背景样式
 */
export function useGradients() {
    const theme = themeManager.getCurrentTheme();
    return {
        hero: theme.gradients.hero,
        card: theme.gradients.card,
        button: theme.gradients.button,
        discount: theme.gradients.discount,
        hover: {
            primary: theme.gradients.hover.primary,
            secondary: theme.gradients.hover.secondary,
            accent: theme.gradients.hover.accent,
        }
    };
}
/**
 * 获取主题变量映射
 * 用于将现有的硬编码颜色替换为主题变量
 */
export function getColorMapping() {
    return {
        // 绿色映射到primary
        'bg-green-50': 'bg-primary-50',
        'bg-green-100': 'bg-primary-100',
        'bg-green-500': 'bg-primary-500',
        'bg-green-600': 'bg-primary-600',
        'bg-green-700': 'bg-primary-700',
        'text-green-600': 'text-primary-600',
        'text-green-700': 'text-primary-700',
        'text-green-800': 'text-primary-800',
        'border-green-200': 'border-primary-200',
        'border-green-300': 'border-primary-300',
        'border-green-500': 'border-primary-500',
        'border-green-600': 'border-primary-600',
        'hover:bg-green-700': 'hover:bg-primary-700',
        'hover:text-green-600': 'hover:text-primary-600',
        'hover:border-green-600': 'hover:border-primary-600',
        // 蓝色映射到secondary
        'bg-blue-50': 'bg-secondary-50',
        'bg-blue-100': 'bg-secondary-100',
        'bg-blue-400': 'bg-secondary-400',
        'bg-blue-500': 'bg-secondary-500',
        'bg-blue-600': 'bg-secondary-600',
        'text-blue-500': 'text-secondary-500',
        'text-blue-600': 'text-secondary-600',
        'border-blue-200': 'border-secondary-200',
        'border-blue-300': 'border-secondary-300',
        // 红色映射到error
        'bg-red-50': 'bg-error-50',
        'bg-red-100': 'bg-error-100',
        'bg-red-500': 'bg-error-500',
        'bg-red-600': 'bg-error-600',
        'bg-red-700': 'bg-error-700',
        'text-red-500': 'text-error-500',
        'text-red-600': 'text-error-600',
        'text-red-700': 'text-error-700',
        'border-red-500': 'border-error-500',
        'from-red-500': 'from-error-500',
        'to-red-600': 'to-error-600',
        'hover:from-red-600': 'hover:from-error-600',
        'hover:to-red-700': 'hover:to-error-700',
        // 灰色映射到neutral
        'bg-gray-50': 'bg-neutral-50',
        'bg-gray-100': 'bg-neutral-100',
        'bg-gray-200': 'bg-neutral-200',
        'bg-gray-400': 'bg-neutral-400',
        'bg-gray-500': 'bg-neutral-500',
        'bg-gray-800': 'bg-neutral-800',
        'bg-gray-900': 'bg-neutral-900',
        'text-gray-400': 'text-neutral-400',
        'text-gray-500': 'text-neutral-500',
        'text-gray-600': 'text-neutral-600',
        'text-gray-700': 'text-neutral-700',
        'text-gray-800': 'text-neutral-800',
        'text-gray-900': 'text-neutral-900',
        'border-gray-200': 'border-neutral-200',
        'border-gray-300': 'border-neutral-300',
    };
}
